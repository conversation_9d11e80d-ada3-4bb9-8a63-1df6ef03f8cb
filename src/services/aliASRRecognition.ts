import { AudioRecorder } from './audioRecorder';
import { API_BASE_URL, STATIC_BASE_URL } from '../config/constants';
import { ConfigService } from '../services/configService';

/**
 * 阿里云语音识别选项
 */
export interface AliASROptions {
  continuous?: boolean;
  interimResults?: boolean;
  lang?: string;
  sampleRate?: number;
  format?: string;
  enablePunctuation?: boolean;
  debug?: boolean;
  resultTimeoutMs?: number; // 结果超时时间，单位毫秒。超过此时间没有新结果时，将当前结果标记为最终结果
  silenceTimeoutMs?: number; // 静音超时时间，单位毫秒。超过此时间没有检测到音频输入时，将当前结果标记为最终结果
  onVolumeChange?: (volume: number) => void; // 音量变化回调函数
}

/**
 * 阿里云语音识别结果事件
 */
export interface AliASRResultEvent {
  results: {
    [index: number]: {
      [index: number]: {
        transcript: string;
        confidence: number;
      };
      isFinal: boolean;
    };
  };
}

/**
 * 阿里云语音识别客户端
 * 模拟WebkitSpeechRecognition接口，但使用阿里云语音识别服务
 * 
 * 支持的配置选项:
 * - continuous: 是否连续识别
 * - interimResults: 是否返回中间结果
 * - lang: 语言代码，默认zh-CN
 * - sampleRate: 采样率，默认16000
 * - format: 音频格式，默认pcm
 * - enablePunctuation: 是否启用标点符号，默认true
 * - debug: 是否启用调试模式，默认false
 * - resultTimeoutMs: 结果超时时间(毫秒)，超过此时间没有新结果时，将当前结果标记为最终结果，默认0(禁用)
 * - silenceTimeoutMs: 静音超时时间(毫秒)，超过此时间没有检测到音频输入时，将当前结果标记为最终结果，默认与resultTimeoutMs相同
 * 
 * 超时机制说明:
 * 1. resultTimeoutMs: 从收到最后一个识别结果开始计时，如果超过此时间没有新的识别结果，则将当前结果标记为最终结果
 * 2. silenceTimeoutMs: 从收到最后一个音频数据包开始计时，如果超过此时间没有新的音频数据，则将当前结果标记为最终结果
 * 
 * 两种超时机制可以同时启用，任一条件满足时都会触发结果最终化，确保在用户停止说话后能及时获得最终结果
 */
export class AliASRRecognition {
  // WebkitSpeechRecognition兼容属性
  public continuous: boolean = false;
  public interimResults: boolean = false;
  public lang: string = 'zh-CN';
  
  // 事件处理器
  public onstart: (() => void) | null = null;
  public onend: (() => void) | null = null;
  public onresult: ((event: AliASRResultEvent) => void) | null = null;
  public onerror: ((event: { error: string }) => void) | null = null;
  public onaudiostart: (() => void) | null = null;
  public onaudioend: (() => void) | null = null;
  public onnomatch: (() => void) | null = null;
  public onsoundstart: (() => void) | null = null;
  public onsoundend: (() => void) | null = null;
  public onspeechstart: (() => void) | null = null;
  public onspeechend: (() => void) | null = null;
  public onreconnected: (() => void) | null = null; // 重连成功回调
  
  // 内部属性
  private audioRecorder: AudioRecorder | null = null;
  private websocket: WebSocket | null = null;
  private isRecording: boolean = false;
  private isConnecting: boolean = false;
  private isConnected: boolean = false;
  private reconnectAttempts: number = 0;
  private maxReconnectAttempts: number = 10;
  private reconnectDelay: number = 1000;
  private results: AliASRResultEvent['results'] = {};
  private resultIndex: number = 0;
  private autoReconnect: boolean = true;
  private lastResultState: any = null;
  private heartbeatInterval: NodeJS.Timeout | null = null;
  private debug: boolean = false;
  private resultTimeoutMs: number = 0; // 结果超时时间，0表示禁用
  private lastAudioTime: number = 0; // 上次收到音频数据的时间
  private lastResultTime: number = 0; // 上次收到结果的时间
  private resultTimeoutId: NodeJS.Timeout | null = null; // 结果超时计时器ID
  private silenceTimeoutMs: number = 0; // 静音超时时间，0表示禁用
  private silenceTimeoutId: NodeJS.Timeout | null = null; // 静音超时计时器ID
  private onVolumeChangeCallback: ((volume: number) => void) | null = null; // 音量变化回调函数

  // 音频数据缓冲相关
  private audioBuffer: ArrayBuffer[] = []; // 音频数据缓冲区
  private maxBufferSize: number = 50; // 最大缓冲区大小（数据包数量）
  private lastWarningTime: number = 0; // 上次警告时间，用于限制警告频率
  private warningInterval: number = 5000; // 警告间隔时间（毫秒）
  
  /**
   * 构造函数
   * @param options 选项
   */
  constructor(options: AliASROptions = {}) {
    this.continuous = options.continuous ?? false;
    this.interimResults = options.interimResults ?? false;
    this.lang = options.lang ?? 'zh-CN';
    this.debug = options.debug ?? false;
    this.resultTimeoutMs = options.resultTimeoutMs ?? 0;
    this.silenceTimeoutMs = options.silenceTimeoutMs ?? this.resultTimeoutMs; // 默认与结果超时时间相同
    this.onVolumeChangeCallback = options.onVolumeChange ?? null; // 初始化音量变化回调

    // 初始化音频录制器
    this.audioRecorder = new AudioRecorder();

    if (this.debug) {
      console.log('阿里云语音识别初始化，调试模式已启用');
      console.log('选项:', options);
      if (this.resultTimeoutMs > 0) {
        console.log(`结果超时时间: ${this.resultTimeoutMs}ms`);
      }
      if (this.silenceTimeoutMs > 0) {
        console.log(`静音超时时间: ${this.silenceTimeoutMs}ms`);
      }
      if (this.onVolumeChangeCallback) {
        console.log('音量变化回调已设置');
      }
    }
  }
  
  /**
   * 开始语音识别
   */
  public async start(): Promise<void> {
    if (this.isRecording) {
      console.log('语音识别已经在运行中');
      return;
    }
    
    try {
      console.log('开始初始化音频录制');
      this.isRecording = true;
      this.autoReconnect = true;
      this.reconnectAttempts = 0;
      
      // 初始化时间戳
      this.lastResultTime = Date.now();
      this.lastAudioTime = Date.now();
      
      // 初始化音频录制
      this.audioRecorder = new AudioRecorder();
      const initialized = await this.audioRecorder.initialize();
      
      if (!initialized) {
        throw new Error('无法初始化音频录制，请检查麦克风权限');
      }
      
      // 连接WebSocket
      console.log('连接WebSocket');
      await this.connectWebSocket();
      
      // 开始音频录制
      console.log('开始音频录制');
      this.audioRecorder.start(
        (data) => this.sendAudioData(data),
        (volume) => this.handleVolumeChange(volume)
      );
      
      // 触发onstart事件
      if (this.onstart) {
        this.onstart();
      }
      
      if (this.onaudiostart) {
        this.onaudiostart();
      }
      
      if (this.onsoundstart) {
        this.onsoundstart();
      }

      // 设置客户端心跳
      this.startHeartbeat();
    } catch (error) {
      console.error('启动语音识别失败:', error);
      this.isRecording = false;
      
      if (this.audioRecorder) {
        this.audioRecorder.release();
        this.audioRecorder = null;
      }
      
      this.closeWebSocket();
      
      // 触发onerror事件
      if (this.onerror) {
        const errorType = error instanceof Error && error.message.includes('麦克风') ? 'not-allowed' : 'network';
        this.onerror({ error: errorType });
      }
      
      throw error;
    }
  }
  
  /**
   * 停止语音识别
   */
  public stop(): void {
    console.log('停止语音识别');
    
    try {
      // 停止自动重连
      this.autoReconnect = false;
      
      // 停止客户端心跳
      this.stopHeartbeat();

      // 停止音频录制
      if (this.audioRecorder) {
        this.audioRecorder.stop();
      }
      
      // 发送停止命令
      if (this.isConnected) {
        this.sendStopCommand();
      }
      
      // 清除结果超时计时器
      if (this.resultTimeoutId) {
        clearTimeout(this.resultTimeoutId);
        this.resultTimeoutId = null;
      }
      
      // 清除静音超时计时器
      if (this.silenceTimeoutId) {
        clearTimeout(this.silenceTimeoutId);
        this.silenceTimeoutId = null;
      }
      
      // 触发onend事件
      if (this.onend) {
        this.onend();
      }
      
      if (this.onaudioend) {
        this.onaudioend();
      }
      
      if (this.onsoundend) {
        this.onsoundend();
      }
    } catch (error) {
      console.error('停止语音识别失败:', error);
    } finally {
      // 关闭WebSocket连接
      this.closeWebSocket();
      
      // 释放音频录制资源
      if (this.audioRecorder) {
        this.audioRecorder.release();
        this.audioRecorder = null;
      }
      
      this.isRecording = false;
      this.results = {};
      this.resultIndex = 0;
      this.lastResultState = null;
    }
  }
  
  /**
   * 中止语音识别
   */
  public abort(): void {
    this.stop();
  }
  
  /**
   * 连接WebSocket
   */
  private async connectWebSocket(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        if (this.isConnecting) {
          console.log('WebSocket连接正在进行中，忽略重复连接请求');
          return;
        }
        
        this.isConnecting = true;
        
        // 创建WebSocket连接
        const baseUrl = STATIC_BASE_URL || API_BASE_URL.replace(/\/api$/, '');
        const wsUrl = `${baseUrl.replace(/^http/, 'ws')}/speech-recognition`;
        console.log('尝试连接WebSocket:', wsUrl);
        
        // 设置连接超时
        const connectionTimeout = setTimeout(() => {
          if (this.websocket && this.websocket.readyState !== WebSocket.OPEN) {
            console.error('WebSocket连接超时');
            this.websocket.close();
            this.isConnecting = false;
            reject(new Error('WebSocket连接超时'));
          }
        }, 5000);
        
        this.websocket = new WebSocket(wsUrl);
        
        // 设置WebSocket事件处理器
        this.websocket.onopen = () => {
          console.log('WebSocket连接已打开');
          clearTimeout(connectionTimeout);
          const wasReconnecting = this.reconnectAttempts > 0; // 记录是否是重连
          this.isConnected = true;
          this.isConnecting = false;
          this.reconnectAttempts = 0;

          // 发送启动命令
          this.sendStartCommand().then(() => {
            // 如果是重连成功，触发重连成功回调
            if (wasReconnecting && this.onreconnected) {
              console.log('WebSocket重连成功，触发重连成功回调');
              this.onreconnected();
            }

            // 成功发送启动命令后解析Promise
            resolve();
          }).catch((error) => {
            // 发送启动命令失败时拒绝Promise
            this.isConnecting = false;
            reject(error);
          });
        };
        
        this.websocket.onclose = (event) => {
          console.log('WebSocket连接已关闭', event.code, event.reason);
          clearTimeout(connectionTimeout);
          this.isConnected = false;
          this.isConnecting = false;
          
          // 如果正在录音且启用了自动重连，尝试重新连接
          if (this.isRecording && this.autoReconnect) {
            if (this.reconnectAttempts < this.maxReconnectAttempts) {
              this.reconnectAttempts++;
              const delay = this.calculateReconnectDelay();
              console.log(`将在${delay}ms后尝试第${this.reconnectAttempts}次重连`);
              
              setTimeout(() => {
                if (this.isRecording && this.autoReconnect) {
                  this.connectWebSocket().catch((error) => {
                    console.error('重连失败:', error);
                    this.handleError('network', '重新连接语音识别服务失败');
                  });
                }
              }, delay);
            } else {
              console.error(`已达到最大重连次数(${this.maxReconnectAttempts})，停止重连`);
              this.handleError('network', '语音识别服务连接断开，达到最大重连次数');
              this.stop();
              reject(new Error('WebSocket连接已关闭，达到最大重连次数'));
            }
          } else if (this.isRecording) {
            this.handleError('network', '语音识别服务连接断开');
            this.stop();
            reject(new Error('WebSocket连接已关闭'));
          } else {
            reject(new Error('WebSocket连接已关闭'));
          }
        };
        
        this.websocket.onerror = (error) => {
          console.error('WebSocket错误:', error);
          clearTimeout(connectionTimeout);
          this.isConnected = false;
          this.isConnecting = false;
          reject(new Error('WebSocket连接错误'));
        };
        
        this.websocket.onmessage = this.handleWebSocketMessage.bind(this);
      } catch (error) {
        console.error('创建WebSocket连接失败:', error);
        this.isConnected = false;
        this.isConnecting = false;
        reject(error);
      }
    });
  }
  
  /**
   * 关闭WebSocket连接
   */
  private closeWebSocket(): void {
    if (this.websocket) {
      try {
        // 移除事件处理器
        this.websocket.onopen = null;
        this.websocket.onclose = null;
        this.websocket.onerror = null;
        this.websocket.onmessage = null;
        
        // 关闭连接
        if (this.websocket.readyState === WebSocket.OPEN || 
            this.websocket.readyState === WebSocket.CONNECTING) {
          console.log('关闭WebSocket连接');
          this.websocket.close();
        }
      } catch (error) {
        console.error('关闭WebSocket连接失败:', error);
      } finally {
        this.websocket = null;
        this.isConnected = false;
      }
    }
  }
  
  /**
   * 发送启动命令
   */
  private async sendStartCommand(): Promise<void> {
    if (!this.websocket || this.websocket.readyState !== WebSocket.OPEN) {
      return;
    }
    
    try {
      // 获取配置
      const configService = ConfigService.getInstance();
      const config = await configService.getConfig();
      
      const startCommand = {
        action: 'start',
        options: {
          sampleRate: config.aliASRSampleRate || 16000,
          format: config.aliASRFormat || 'pcm',
          enablePunctuation: config.aliASREnablePunctuation !== false,
          enableInterimResult: config.aliASREnableInterimResult !== false || this.interimResults
        }
      };
      
      console.log('发送启动命令:', startCommand);
      this.websocket.send(JSON.stringify(startCommand));
    } catch (error) {
      console.error('发送启动命令失败:', error);
      this.handleError('server', '发送启动命令失败');
    }
  }
  
  /**
   * 发送停止命令
   */
  private sendStopCommand(): void {
    if (!this.websocket || this.websocket.readyState !== WebSocket.OPEN) {
      return;
    }
    
    const stopCommand = {
      action: 'stop'
    };
    
    this.websocket.send(JSON.stringify(stopCommand));
  }
  
  /**
   * 发送音频数据
   * @param data 音频数据
   */
  private sendAudioData(data: ArrayBuffer): void {
    if (!this.websocket || this.websocket.readyState !== WebSocket.OPEN || !this.isConnected) {
      // 记录连接状态
      if (!this.websocket) {
        console.warn('无法发送音频数据: WebSocket未初始化');
      } else if (this.websocket.readyState !== WebSocket.OPEN) {
        console.warn(`无法发送音频数据: WebSocket状态不是OPEN (${this.websocket.readyState})`);
      } else if (!this.isConnected) {
        console.warn('无法发送音频数据: WebSocket未连接');
      }
      return;
    }
    
    try {
      // 更新最后收到音频数据的时间
      this.lastAudioTime = Date.now();
      
      // 检查是否需要设置静音超时
      this.checkAndSetSilenceTimeout();
      
      this.websocket.send(data);
    } catch (error) {
      console.error('发送音频数据失败:', error);
    }
  }
  
  /**
   * 检查并设置静音超时
   */
  private checkAndSetSilenceTimeout(): void {
    // 如果未启用静音超时，则不处理
    if (this.silenceTimeoutMs <= 0 || !this.results[this.resultIndex]) {
      return;
    }
    
    // 如果当前结果已经是最终结果，则不需要设置超时
    if (this.results[this.resultIndex].isFinal) {
      return;
    }
    
    // 清除之前的静音超时计时器
    if (this.silenceTimeoutId) {
      clearTimeout(this.silenceTimeoutId);
    }
    
    // 设置新的静音超时计时器
    // 计算从当前时间到最后一次音频数据时间加上静音超时时间的时间差
    const now = Date.now();
    const timeElapsed = now - this.lastAudioTime;
    const remainingTime = Math.max(0, this.silenceTimeoutMs - timeElapsed);
    
    this.silenceTimeoutId = setTimeout(() => {
      console.log(`静音超时(${this.silenceTimeoutMs}ms)，将当前结果标记为最终结果`);
      
      // 只有当有未最终化的结果时才强制最终化
      if (this.results[this.resultIndex] && !this.results[this.resultIndex].isFinal) {
        this.forceResultFinal();
      }
    }, remainingTime);
    
    if (this.debug) {
      console.log(`设置静音超时计时器: ${remainingTime}ms (原始超时: ${this.silenceTimeoutMs}ms, 已经过: ${timeElapsed}ms)`);
    }
  }
  
  /**
   * 处理WebSocket消息
   * @param event WebSocket消息事件
   */
  private handleWebSocketMessage(event: MessageEvent): void {
    try {
      if (this.debug) {
        console.log('收到WebSocket消息:', event.data);
      } else {
        console.log('收到WebSocket消息');
      }
      
      const message = JSON.parse(event.data);
      
      switch (message.event) {
        case 'start':
          // 语音识别服务已启动
          console.log('语音识别服务已启动');
          if (this.onspeechstart) {
            this.onspeechstart();
          }
          break;
          
        case 'result':
          // 语音识别结果
          if (this.debug) {
            console.log('收到识别结果:', message.data);
          }
          this.handleRecognitionResult(message.data);
          break;
          
        case 'end':
          // 语音识别服务已结束
          console.log('语音识别服务已结束');
          if (this.onspeechend) {
            this.onspeechend();
          }
          
          // 如果是因为服务端主动结束，而客户端仍在录音，则尝试重新连接
          if (this.isRecording && this.autoReconnect) {
            console.log('服务端结束会话，但客户端仍在录音，尝试重新连接');
            this.reconnectWebSocket();
          }
          break;
          
        case 'error':
          // 语音识别错误
          console.error('语音识别服务错误:', message.data);
          
          // 如果是超时错误，尝试重新连接
          if (message.data.message && message.data.message.includes('timeout')) {
            console.log('检测到超时错误，尝试重新连接...');
            this.reconnectWebSocket();
          } else {
            this.handleError('server', message.data.message || '语音识别服务错误');
            
            // 对于其他错误，也尝试重新连接
            if (this.isRecording && this.autoReconnect) {
              console.log('检测到服务端错误，尝试重新连接...');
              this.reconnectWebSocket();
            }
          }
          break;
          
        default:
          console.warn('未知的WebSocket消息类型:', message);
      }
    } catch (error) {
      console.error('处理WebSocket消息失败:', error);
      
      // 尝试解析失败，可能是因为收到了非JSON格式的消息
      // 这种情况下不需要特殊处理，继续监听后续消息
      if (this.debug) {
        console.log('原始消息内容:', event.data);
      }
    }
  }
  
  /**
   * 重新连接WebSocket
   */
  private async reconnectWebSocket(): Promise<void> {
    try {
      if (!this.isRecording || !this.autoReconnect) {
        console.log('未启用录音或自动重连，忽略重连请求');
        return;
      }
      
      console.log('准备重新连接WebSocket...');
      
      // 关闭当前连接
      this.closeWebSocket();
      
      // 等待一段时间再重新连接
      const delay = this.calculateReconnectDelay();
      console.log(`将在${delay}ms后尝试重连`);
      await new Promise(resolve => setTimeout(resolve, delay));
      
      // 检查是否仍然需要重连
      if (!this.isRecording || !this.autoReconnect) {
        console.log('重连等待期间状态已改变，取消重连');
        return;
      }
      
      // 重新连接
      console.log('尝试重新连接WebSocket...');
      await this.connectWebSocket();
      
      console.log('WebSocket重新连接成功');
      
      // 恢复之前的状态
      if (this.lastResultState) {
        console.log('恢复之前的识别状态');
        this.results = this.lastResultState.results || {};
        this.resultIndex = this.lastResultState.resultIndex || 0;
      }
      
      // 重置重连计数器
      if (this.reconnectAttempts > 0) {
        console.log(`重连成功，重置重连计数器(之前: ${this.reconnectAttempts})`);
        this.reconnectAttempts = 0;
      }

      // 触发重连成功回调
      if (this.onreconnected) {
        console.log('触发重连成功回调');
        this.onreconnected();
      }
    } catch (error) {
      console.error('WebSocket重新连接失败:', error);
      
      // 增加重连尝试次数
      this.reconnectAttempts++;
      
      if (this.reconnectAttempts < this.maxReconnectAttempts) {
        console.log(`重连失败，将在稍后尝试第${this.reconnectAttempts + 1}次重连`);
        // 下一次重连将由onclose事件处理
        
        // 如果仍然在录音且启用了自动重连，则安排下一次重连
        if (this.isRecording && this.autoReconnect) {
          const nextDelay = this.calculateReconnectDelay();
          console.log(`安排下一次重连，延迟: ${nextDelay}ms`);
          setTimeout(() => {
            if (this.isRecording && this.autoReconnect) {
              this.reconnectWebSocket();
            }
          }, nextDelay);
        }
      } else {
        console.error(`已达到最大重连次数(${this.maxReconnectAttempts})，停止重连`);
        this.handleError('network', '重新连接语音识别服务失败，达到最大重连次数');
        
        // 尝试一次性重置，以便用户可以手动重试
        setTimeout(() => {
          console.log('重置重连状态，允许用户手动重试');
          this.reconnectAttempts = 0;
        }, 30000); // 30秒后重置
      }
    }
  }
  
  /**
   * 计算重连延迟时间（指数退避策略）
   */
  private calculateReconnectDelay(): number {
    // 基础延迟时间
    const baseDelay = this.reconnectDelay;
    
    // 指数退避策略：基础延迟 * (2^尝试次数)，并添加一些随机性
    const exponentialDelay = baseDelay * Math.pow(2, Math.min(this.reconnectAttempts, 6));
    
    // 添加随机抖动，避免多个客户端同时重连
    const jitter = Math.random() * 1000;
    
    // 计算最终延迟，并限制最大值为30秒
    return Math.min(exponentialDelay + jitter, 30000);
  }
  
  /**
   * 处理识别结果
   * @param result 识别结果
   */
  private handleRecognitionResult(result: { text: string; isFinal: boolean; confidence?: number }): void {
    try {
      // 如果没有文本，忽略结果
      if (!result.text || result.text.trim() === '') {
        return;
      }
      
      // 更新最后收到结果的时间
      this.lastResultTime = Date.now();
      
      // 保留这里的日志，作为主要日志输出点
      console.log('处理识别结果:', result);
      
      // 创建识别结果对象
      const transcript = {
        transcript: result.text,
        confidence: result.confidence || 1.0
      };
      
      // 更新结果集
      if (!this.results[this.resultIndex]) {
        // 创建一个带有isFinal属性的数组
        const resultArray: any[] = [transcript];
        Object.defineProperty(resultArray, 'isFinal', {
          value: result.isFinal,
          writable: true,
          enumerable: true
        });
        this.results[this.resultIndex] = resultArray as any;
      } else if (result.isFinal && !this.results[this.resultIndex].isFinal) {
        // 更新为最终结果
        this.results[this.resultIndex][0] = transcript;
        this.results[this.resultIndex].isFinal = true;
      } else if (result.isFinal) {
        // 如果已经是最终结果，创建新的结果索引
        this.resultIndex++;
        // 创建一个带有isFinal属性的数组
        const resultArray: any[] = [transcript];
        Object.defineProperty(resultArray, 'isFinal', {
          value: true,
          writable: true,
          enumerable: true
        });
        this.results[this.resultIndex] = resultArray as any;
      } else {
        // 更新中间结果
        this.results[this.resultIndex][0] = transcript;
      }
      
      // 创建结果事件
      const event: AliASRResultEvent = {
        results: { ...this.results }
      };
      
      // 保存当前状态，用于重连恢复
      this.lastResultState = {
        results: { ...this.results },
        resultIndex: this.resultIndex
      };
      
      // 触发结果事件
      if (this.onresult) {
        // 减少日志输出，只在调试模式下打印详细事件对象
        if (this.debug) {
          console.log('触发onresult事件:', event);
        } else {
          console.log('触发onresult事件');
        }
        this.onresult(event);
      }
      
      // 如果是最终结果，准备接收下一个结果
      if (result.isFinal) {
        console.log('收到最终结果，准备接收下一个结果');
        
        // 清除超时计时器
        if (this.resultTimeoutId) {
          clearTimeout(this.resultTimeoutId);
          this.resultTimeoutId = null;
        }
      } else if (this.resultTimeoutMs > 0) {
        // 如果不是最终结果，并且启用了结果超时，设置超时计时器
        // 先清除之前的计时器
        if (this.resultTimeoutId) {
          clearTimeout(this.resultTimeoutId);
        }
        
        // 设置新的计时器
        // 计算从当前时间到最后一次结果时间加上结果超时时间的时间差
        const now = Date.now();
        const timeElapsed = now - this.lastResultTime;
        const remainingTime = Math.max(0, this.resultTimeoutMs - timeElapsed);
        
        this.resultTimeoutId = setTimeout(() => {
          console.log(`结果超时(${this.resultTimeoutMs}ms)，将当前结果标记为最终结果`);
          this.forceResultFinal();
        }, remainingTime);
        
        if (this.debug) {
          console.log(`设置结果超时计时器: ${remainingTime}ms (原始超时: ${this.resultTimeoutMs}ms, 已经过: ${timeElapsed}ms)`);
        }
      }
    } catch (error) {
      console.error('处理识别结果失败:', error);
    }
  }
  
  /**
   * 处理音量变化
   * @param volume 音量值(0-100)
   */
  private handleVolumeChange(volume: number): void {
    // 如果设置了音量变化回调，则调用它
    if (this.onVolumeChangeCallback) {
      this.onVolumeChangeCallback(volume);
    }
  }
  
  /**
   * 处理错误
   * @param errorType 错误类型
   * @param errorMessage 错误消息
   */
  private handleError(errorType: string, errorMessage: string): void {
    console.error(`语音识别错误(${errorType}):`, errorMessage);
    
    if (this.onerror) {
      this.onerror({ error: errorType });
    }
  }
  
  /**
   * 启动客户端心跳
   */
  private startHeartbeat(): void {
    // 停止现有心跳
    this.stopHeartbeat();
    
    // 设置新的心跳，每10秒发送一次
    this.heartbeatInterval = setInterval(() => {
      if (this.isConnected && this.websocket && this.websocket.readyState === WebSocket.OPEN) {
        try {
          // 发送一个简单的ping消息作为心跳
          // 使用空的音频数据包作为心跳，与服务端兼容
          const heartbeatData = new ArrayBuffer(320); // 10ms的16kHz 16bit PCM数据
          this.websocket.send(heartbeatData);
          console.log('发送客户端心跳(音频数据格式)');
        } catch (error) {
          console.error('发送心跳失败:', error);
        }
      }
    }, 10000);
  }
  
  /**
   * 停止客户端心跳
   */
  private stopHeartbeat(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
  }
  
  /**
   * 检查浏览器是否支持语音识别
   */
  public static isSupported(): boolean {
    return AudioRecorder.isSupported();
  }
  
  /**
   * 强制将当前结果标记为最终结果
   */
  private forceResultFinal(): void {
    try {
      // 检查是否有当前结果
      if (!this.results[this.resultIndex] || this.results[this.resultIndex].isFinal) {
        return;
      }
      
      console.log('强制将当前结果标记为最终结果');
      
      // 将当前结果标记为最终结果
      this.results[this.resultIndex].isFinal = true;
      
      // 创建结果事件
      const event: AliASRResultEvent = {
        results: { ...this.results }
      };
      
      // 保存当前状态，用于重连恢复
      this.lastResultState = {
        results: { ...this.results },
        resultIndex: this.resultIndex
      };
      
      // 触发结果事件
      if (this.onresult) {
        if (this.debug) {
          console.log('触发强制最终结果事件:', event);
        } else {
          console.log('触发强制最终结果事件');
        }
        this.onresult(event);
      }
      
      // 清除超时计时器
      if (this.resultTimeoutId) {
        clearTimeout(this.resultTimeoutId);
        this.resultTimeoutId = null;
      }
    } catch (error) {
      console.error('强制将当前结果标记为最终结果失败:', error);
    }
  }
} 